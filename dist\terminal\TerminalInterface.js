import readline from 'readline';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { ConfigManager } from '../config/ConfigManager.js';
import { SessionManager } from '../session/SessionManager.js';
import { DeepseekProvider } from '../providers/DeepseekProvider.js';
import { OllamaProvider } from '../providers/OllamaProvider.js';
import { OnboardingComponent } from './components/OnboardingComponent.js';
import { HeaderComponent } from './components/HeaderComponent.js';
import { SlashCommandsComponent } from './components/SlashCommandsComponent.js';
import { MessageHistoryComponent } from './components/MessageHistoryComponent.js';
import { ChatInputComponent } from './components/ChatInputComponent.js';
import { ThinkingAnimation, StatusIndicator } from './components/ThinkingAnimation.js';
import { OutputFormatter } from '../utils/OutputFormatter.js';
import { OutputProcessor } from '../utils/OutputProcessor.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
export class TerminalInterface {
    configManager;
    sessionManager;
    state;
    deepseekProvider;
    ollamaProvider;
    slashCommands;
    messageHistory;
    chatInput;
    thinkingAnimation;
    errorHandler;
    rl;
    constructor() {
        this.configManager = new ConfigManager();
        this.sessionManager = new SessionManager();
        this.errorHandler = new ErrorHandler();
        this.thinkingAnimation = new ThinkingAnimation();
        this.state = {
            isThinking: false,
            currentSession: '', // Will be set during initialization
            messageHistory: [],
            workingDirectory: process.cwd(),
            provider: 'deepseek',
            model: 'deepseek-chat'
        };
        this.slashCommands = new SlashCommandsComponent(this.configManager, this.state, this.sessionManager);
        this.messageHistory = new MessageHistoryComponent({
            showTimestamps: true,
            showToolDetails: true,
            maxDisplayLength: 150
        });
        this.chatInput = new ChatInputComponent({
            prompt: this.getPrompt(),
            multiline: true,
            maxLength: 10000,
            placeholder: 'Type your message or command...',
            autoComplete: ['/help', '/model', '/provider', '/session', '/history', '/exit']
        });
    }
    async start() {
        try {
            // Initialize session manager
            await this.sessionManager.initialize();
            // Load configuration
            const config = await this.configManager.loadConfig();
            this.updateStateFromConfig(config);
            // Check if setup is needed
            if (!config.providers.deepseek && !config.providers.ollama) {
                const onboarding = new OnboardingComponent(this.configManager);
                await onboarding.start();
                // Reload config after onboarding
                const updatedConfig = await this.configManager.loadConfig();
                this.updateStateFromConfig(updatedConfig);
            }
            // Initialize or load session
            await this.initializeSession();
            // Initialize providers
            this.initializeProviders();
            // Start interactive mode
            await this.startInteractiveMode();
        }
        catch (error) {
            this.errorHandler.logError(error, 'Terminal startup');
            process.exit(1);
        }
    }
    updateStateFromConfig(config) {
        this.state.provider = config.currentProvider;
        this.state.model = config.currentModel;
        this.state.workingDirectory = config.workingDirectory;
        this.state.currentSession = config.currentSessionId || '';
        // For backward compatibility, load from sessionHistory if no current session
        if (!this.state.currentSession && config.sessionHistory?.length > 0) {
            this.state.messageHistory = config.sessionHistory;
        }
        // Update message history component
        this.messageHistory.setMessages(this.state.messageHistory);
        // Update chat input prompt
        this.chatInput.updatePrompt(this.getPrompt());
    }
    async initializeSession() {
        try {
            const config = this.configManager.getConfig();
            // Check if we have a current session ID and it exists
            if (config.currentSessionId) {
                const sessionData = await this.sessionManager.loadSession(config.currentSessionId);
                if (sessionData) {
                    // Load existing session
                    this.state.currentSession = config.currentSessionId;
                    this.state.messageHistory = sessionData.messages;
                    this.state.provider = sessionData.metadata.provider;
                    this.state.model = sessionData.metadata.model;
                    this.state.workingDirectory = sessionData.metadata.workingDirectory;
                    // Update message history component
                    this.messageHistory.setMessages(this.state.messageHistory);
                    return;
                }
            }
            // Create new session if no valid session exists or auto-create is enabled
            if (config.autoCreateSession !== false) {
                const sessionId = await this.sessionManager.createNewSession(this.state.provider, this.state.model, this.state.workingDirectory);
                this.state.currentSession = sessionId;
                this.state.messageHistory = [];
                // Update config with new session
                await this.configManager.updateCurrentSession(sessionId);
                // Migrate old session history if it exists
                if (config.sessionHistory?.length > 0) {
                    for (const message of config.sessionHistory) {
                        await this.sessionManager.addMessageToSession(sessionId, message);
                    }
                    this.state.messageHistory = config.sessionHistory;
                    // Clear old history after migration
                    await this.configManager.clearHistory();
                }
            }
            else {
                // Use fallback session ID if auto-create is disabled
                this.state.currentSession = `session_${Date.now()}`;
            }
            // Update message history component
            this.messageHistory.setMessages(this.state.messageHistory);
        }
        catch (error) {
            console.error('Failed to initialize session:', error);
            // Fallback to basic session
            this.state.currentSession = `session_${Date.now()}`;
            this.state.messageHistory = [];
        }
    }
    initializeProviders() {
        const config = this.configManager.getConfig();
        if (config.providers.deepseek) {
            this.deepseekProvider = new DeepseekProvider(config.providers.deepseek);
        }
        if (config.providers.ollama) {
            this.ollamaProvider = new OllamaProvider(config.providers.ollama);
        }
    }
    async startInteractiveMode() {
        console.clear();
        console.log(HeaderComponent.renderBanner());
        console.log();
        console.log(HeaderComponent.render(this.state));
        console.log();
        StatusIndicator.success('Interactive mode started. Type /help for commands or start chatting!');
        console.log();
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: this.getPrompt()
        });
        // Handle Ctrl+C
        this.rl.on('SIGINT', () => {
            if (this.state.isThinking) {
                this.thinkingAnimation.stop();
                this.state.isThinking = false;
                console.log(chalk.yellow('\n⚠️  Operation interrupted'));
                this.rl.prompt();
            }
            else {
                console.log(chalk.cyan('\n👋 Goodbye!'));
                process.exit(0);
            }
        });
        // Handle input
        this.rl.on('line', async (input) => {
            await this.handleInput(input.trim());
        });
        // Handle close
        this.rl.on('close', () => {
            console.log(chalk.cyan('\n👋 Goodbye!'));
            process.exit(0);
        });
        this.rl.prompt();
    }
    async handleInput(input) {
        if (!input) {
            this.rl.prompt();
            return;
        }
        try {
            // Handle slash commands
            if (this.slashCommands.isSlashCommand(input)) {
                const handled = await this.slashCommands.executeCommand(input);
                if (handled) {
                    this.rl.setPrompt(this.getPrompt());
                    this.rl.prompt();
                    return;
                }
            }
            // Handle regular chat
            await this.handleChatMessage(input);
        }
        catch (error) {
            this.errorHandler.logError(error, 'Input handling');
        }
        this.rl.setPrompt(this.getPrompt());
        this.rl.prompt();
    }
    async handleChatMessage(input) {
        // Add user message to history
        const userMessage = {
            id: this.generateId(),
            role: 'user',
            content: input,
            timestamp: new Date()
        };
        this.state.messageHistory.push(userMessage);
        this.messageHistory.addMessage(userMessage);
        // Note: Don't save to config here, we'll save after getting the response
        console.log(OutputFormatter.formatUserMessage(input));
        // Get current provider
        const currentProvider = this.getCurrentProvider();
        if (!currentProvider) {
            StatusIndicator.error('No provider available. Please configure a provider first.');
            return;
        }
        // Start thinking animation
        this.state.isThinking = true;
        this.thinkingAnimation.start('Processing your request');
        try {
            // Get response from LLM
            const response = await currentProvider.chat(this.state.messageHistory, this.state.model);
            // Stop thinking animation
            this.thinkingAnimation.stop();
            this.state.isThinking = false;
            // Process and display response using OutputProcessor
            const config = this.configManager.getConfig();
            const processedResponse = OutputProcessor.processAssistantMessage(response, {
                colorScheme: 'default',
                highlightKeywords: ['error', 'success', 'warning', 'completed', 'failed'],
                formatJson: true,
                showLineNumbers: false,
                showToolDetails: config.showToolDetails,
                showToolExecutionDetails: config.showToolExecutionDetails
            });
            console.log(processedResponse.formatted);
            // Add response to history
            this.state.messageHistory.push(response);
            this.messageHistory.addMessage(response);
            // Save both user message and response to session
            if (this.state.currentSession) {
                try {
                    await this.sessionManager.addMessageToSession(this.state.currentSession, userMessage);
                    await this.sessionManager.addMessageToSession(this.state.currentSession, response);
                }
                catch (error) {
                    console.error('Failed to save messages to session:', error);
                    // Fallback to old method
                    await this.configManager.addMessageToHistory(userMessage);
                    await this.configManager.addMessageToHistory(response);
                }
            }
            else {
                // Fallback to old method
                await this.configManager.addMessageToHistory(userMessage);
                await this.configManager.addMessageToHistory(response);
            }
        }
        catch (error) {
            this.thinkingAnimation.stop();
            this.state.isThinking = false;
            if (error.message === 'APPROVAL_REQUIRED') {
                await this.handleApprovalRequired();
            }
            else {
                this.errorHandler.logError(error, 'Chat processing');
            }
        }
    }
    async handleApprovalRequired() {
        const { approve } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'approve',
                message: 'This command requires approval. Do you want to proceed?',
                default: false
            }
        ]);
        if (approve) {
            StatusIndicator.info('Command approved. Executing...');
            // Re-execute with approval
            // This would need to be implemented based on the specific command context
        }
        else {
            StatusIndicator.info('Command cancelled by user.');
        }
    }
    getCurrentProvider() {
        if (this.state.provider === 'deepseek') {
            return this.deepseekProvider;
        }
        else if (this.state.provider === 'ollama') {
            return this.ollamaProvider;
        }
        return undefined;
    }
    getPrompt() {
        const status = this.state.isThinking ? chalk.red('●') : chalk.green('●');
        const provider = chalk.yellow(this.state.provider.substring(0, 3));
        const cwd = chalk.blue(this.getCurrentDirectoryName());
        return `${status} ${provider} ${cwd} ${chalk.cyan('>')} `;
    }
    getCurrentDirectoryName() {
        const parts = this.state.workingDirectory.split(/[/\\]/);
        return parts[parts.length - 1] || 'root';
    }
    generateId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    async stop() {
        if (this.thinkingAnimation.isActive()) {
            this.thinkingAnimation.stop();
        }
        if (this.rl) {
            this.rl.close();
        }
    }
}
//# sourceMappingURL=TerminalInterface.js.map